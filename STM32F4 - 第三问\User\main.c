/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 第二问专用：AD9834高性能DDS信号生成 (替换内置DAC方案)
#include "../Modules/Generation/ad9834_highperf.h"  // AD9834 DDS模块驱动
// #include "../Modules/Generation/dac8552.h"     // 禁用DAC8552 (已被AD9834替换)
// #include "../Modules/Generation/dds_wavegen.h" // 禁用内置DDS (已被AD9834替换)

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9834配置变量 (替代原有DDS系统)
// 注释掉未使用的变量，避免编译警告
// static uint32_t current_frequency = 3000000;  // 当前频率 (默认3MHz)
// static uint16_t current_wave_type = SINE_WAVE; // 当前波形 (默认正弦波)

// AD9834技术优势：
// - 专业DDS芯片，75MHz系统时钟
// - 28位频率分辨率 (0.028Hz精度)
// - 12位相位分辨率 (0.088°精度)
// - 1Hz-5MHz频率范围，远超内置DAC方案
// 所有表都使用内存对齐优化，支持硬件加速访问

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void DDS_HighPerf_Init(void);
// 移除不需要的函数声明，专注于稳定输出

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* ==================== 第三问测试：1kHz信号输出 ==================== */
    // 初始化AD9834 DDS模块 - 第三问测试配置
    AD9834_Init();  // 初始化AD9834

    // 验证AD9834配置
    Delay_ms(100);  // 等待AD9834稳定

    // 第三问测试：设置1kHz正弦波输出 (忽略幅度问题)
    AD9834_SetFrequency(FREQ_REG_0, 1000.0f, SINE_WAVE);
    Delay_ms(50);  // 等待第一次设置稳定

    // 再次设置确保稳定性
    AD9834_SetFrequency(FREQ_REG_0, 1000.0f, SINE_WAVE);
    Delay_ms(50);  // 等待第二次设置稳定

    // 最终稳定性确认
    AD9834_StabilizeFrequency(1000);
    Delay_ms(20);  // 最终稳定时间

    // LED快闪3次表示AD9834初始化成功
    for (int i = 0; i < 6; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(100);
    }

    /* ==================== 第三问测试配置完成！==================== */
    // 当前输出：1kHz正弦波，约210mV峰峰值，AD9834模块输出
    // 测试目标：观察电路对1kHz信号的响应
    // 技术优势：AD9834专业DDS芯片，高精度频率控制
    // 测试重点：电路响应特性，暂时忽略幅度控制问题

    /* 主循环 - 超稳定5MHz输出 (最小干扰模式) */
    uint32_t led_counter = 0;
    uint32_t stability_check_counter = 0;

    while (1)
    {
        led_counter++;
        stability_check_counter++;

        // ==================== 长期稳定性检查 ====================
        // 每60秒重新稳定一次频率，防止长期漂移
        if (stability_check_counter >= 60000000) {  // 约60秒
            AD9834_StabilizeFrequency(1000);  // 重新稳定1kHz
            stability_check_counter = 0;
        }

        // ==================== 最小化LED干扰 ====================
        // 大幅降低LED闪烁频率，减少对AD9834的电磁干扰
        if (led_counter % 2000000 == 0) {  // 很慢的闪烁
            if (GPIO_ReadOutputDataBit(GPIOE, GPIO_Pin_6)) {
                GPIO_ResetBits(GPIOE, GPIO_Pin_6);
            } else {
                GPIO_SetBits(GPIOE, GPIO_Pin_6);
            }
        }

        // ==================== 超低功耗模式 ====================
        // 使用WFI指令让CPU进入低功耗状态，减少系统噪声
        __WFI();  // 等待中断，CPU进入睡眠，降低功耗和噪声
    }
}

// ==================== 第三问测试说明 ====================
//
// 第三问测试配置：1kHz正弦波输出
// 测试目标：观察电路对1kHz信号的响应特性
//
// AD9834技术优势：
// 1. 专业DDS芯片，75MHz系统时钟
// 2. 28位频率分辨率，精度达0.028Hz
// 3. 支持1Hz-5MHz全频率范围
// 4. 硬件生成，CPU占用率接近0
// 5. 多种波形：正弦波、三角波、方波
// 6. 双频率寄存器支持快速切换
//
// 硬件连接：
// PA3 -> AD9834_FSYNC   (帧同步)
// PA4 -> AD9834_SCLK    (串行时钟)
// PA5 -> AD9834_SDATA   (串行数据)
// PA6 -> AD9834_RESET   (复位信号)
// PB0 -> AD9834_FSELECT (频率选择)
// PB1 -> AD9834_PSELECT (相位选择)
//
// 输出信号：AD9834模块的IOUT引脚，1kHz正弦波
// 测试重点：电路响应，暂时忽略幅度控制








/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


