# STM32F4 第三问测试指南

## 📋 测试概述

**测试目标**: 验证AD9834在1kHz下的信号输出，观察电路响应特性
**测试策略**: 暂时忽略幅度控制问题，专注频率功能验证

## 🔧 硬件准备

### 1. STM32F4开发板连接
```
AD9834模块连接:
PA3 → AD9834_FSYNC   (帧同步)
PA4 → AD9834_SCLK    (串行时钟)  
PA5 → AD9834_SDATA   (串行数据)
PA6 → AD9834_RESET   (复位信号)
PB0 → AD9834_FSELECT (频率选择)
PB1 → AD9834_PSELECT (相位选择)
5V  → VCC, GND → GND
```

### 2. 测试设备
- 示波器 (观察信号质量)
- 万用表 (测量电压)
- 被测电路 (观察响应)

## 💻 软件配置

### 1. 代码修改状态
- ✅ 频率设置: 5MHz → 1kHz
- ✅ 稳定性配置: 多重确认机制
- ✅ 编译状态: 无错误无警告

### 2. 关键配置参数
```c
// 目标频率: 1kHz
AD9834_SetFrequency(FREQ_REG_0, 1000.0f, SINE_WAVE);

// 稳定性确认
AD9834_StabilizeFrequency(1000);
```

## 🧪 测试步骤

### 步骤1: 程序烧录
1. 打开Keil项目: `STM32F4 - 第三问/project.uvprojx`
2. 编译项目 (应该0错误0警告)
3. 连接ST-Link调试器
4. 烧录程序到STM32F4

### 步骤2: 硬件验证
1. 上电后观察LED闪烁 (6次快闪表示初始化成功)
2. 用示波器连接AD9834的IOUT引脚
3. 确认输出1kHz正弦波信号

### 步骤3: 信号质量测试
1. **频率测量**: 应为1000Hz ±1Hz
2. **幅度测量**: 约210mV峰峰值 (暂时忽略)
3. **波形质量**: 正弦波，无明显失真
4. **稳定性**: 长时间观察频率稳定性

### 步骤4: 电路响应测试
1. 连接被测电路到AD9834输出
2. 观察电路对1kHz信号的响应
3. 记录响应特性和参数

## 📊 测试记录表

```
测试日期: ___________
测试人员: ___________

=== AD9834输出测试 ===
设定频率: 1000Hz
实测频率: _________Hz
频率误差: _________%
输出幅度: _________mV
波形质量: _________
THD: ____________%

=== 稳定性测试 ===
测试时长: _________分钟
频率漂移: _________Hz
温度影响: _________
长期稳定性: _______

=== 电路响应测试 ===
输入信号: 1kHz, ___mV
输出信号: _____Hz, ___mV
相位差: __________度
增益: ____________dB
响应特性: _________

=== 系统状态 ===
LED指示: __________
编译状态: 0错误0警告
烧录状态: _________
运行状态: _________
```

## ⚠️ 注意事项

### 1. 安全提醒
- 确保电源电压正确 (5V)
- 避免短路和反接
- 示波器探头正确连接

### 2. 测试要点
- 重点关注频率精度，暂时忽略幅度
- 观察长期稳定性 (至少10分钟)
- 记录任何异常现象

### 3. 问题排查
- 如果无输出: 检查硬件连接和电源
- 如果频率不对: 检查AD9834配置
- 如果波形失真: 检查负载和接地

## 🎯 预期结果

### 正常情况下应该观察到:
- ✅ 1kHz ±0.01% 的稳定正弦波输出
- ✅ 约210mV峰峰值 (幅度暂时忽略)
- ✅ THD < 1% 的良好波形质量
- ✅ 长期稳定的频率输出
- ✅ LED正常闪烁指示

### 如果出现问题:
- 参考第二问的稳定配置
- 检查SPI通信时序
- 验证AD9834模块工作状态

## 📝 测试完成后

1. **记录测试结果**: 填写完整的测试记录表
2. **保存波形截图**: 示波器截图保存
3. **更新会话记录**: 在会话记录中记录测试结果
4. **准备下一步**: 根据测试结果规划后续工作

---

**版权所有 © 2024 STM32F4第三问测试团队**
