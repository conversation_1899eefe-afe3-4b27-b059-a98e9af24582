<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Using CMSIS with generic ARM Processors</title>
<title>CMSIS-CORE: Using CMSIS with generic ARM Processors</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_using__a_r_m_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Using CMSIS with generic ARM Processors </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>ARM provides CMSIS-CORE files for the supported ARM Processors and for various compiler vendors. These files can be used when standard ARM processors should be used in a project. The table below lists the folder and device names of the ARM processors.</p>
<table  class="cmtable">
<tr>
<th>Folder </th><th>Processor </th><th>Description  </th></tr>
<tr>
<td>".\Device\ARM\ARMCM0" </td><td>Cortex-M0 </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the Cortex-M0 processor. The device name is ARMCM0 and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMCM0.h&gt;.   </td></tr>
<tr>
<td>".\Device\ARM\ARMCM0plus" </td><td>Cortex-M0+ </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the Cortex-M0+ processor. The device name is ARMCM0plus and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMCM0plus.h&gt;.   </td></tr>
<tr>
<td>".\Device\ARM\ARMCM3" </td><td>Cortex-M3 </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the Cortex-M3 processor. The device name is ARMCM3 and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMCM3.h&gt;.   </td></tr>
<tr>
<td>".\Device\ARM\ARMCM4" </td><td>Cortex-M4 </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the Cortex-M4 processor. The device name is ARMCM4 and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMCM4.h&gt;.   </td></tr>
<tr>
<td>".\Device\ARM\ARMCM7" </td><td>Cortex-M7 </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the Cortex-M7 processor. The device name is ARMCM7 and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMCM7.h&gt;.   </td></tr>
<tr>
<td>".\Device\ARM\ARMSC000" </td><td>SecurCore SC000 </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the SecurCore SC000 processor. The device name is ARMSC000 and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMSC000.h&gt;.   </td></tr>
<tr>
<td>".\Device\ARM\ARMSC300" </td><td>SecurCore SC300 </td><td>Contains <b>Include</b> and <b>Source</b> template files configured for the SecurCore SC300 processor. The device name is ARMSC300 and the name of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is &lt;ARMSC300.h&gt;.   </td></tr>
</table>
<h1><a class="anchor" id="Using_ARM_Lib_sec"></a>
Create generic Libraries with CMSIS</h1>
<p>The CMSIS Processor and Core Peripheral files allow also to create generic libraries. The <a href="../../DSP/html/index.html"><b>CMSIS-DSP</b> </a> Libraries are an example for such a generic library.</p>
<p>To build a generic Library set the define <b><b>CMSIS_GENERIC</b> and include the relevant <b>core_&lt;cpu&gt;.h</b> CMSIS CPU &amp; Core Access header file for the processor. The define <b></b>CMSIS_GENERIC</b> disables device-dependent features such as the <b>SysTick</b> timer and the <b>Interrupt System</b>. Refer to <a class="el" href="device_h_pg.html#core_config_sect">Configuration of the Processor and Core Peripherals</a> for a list of the available <b>core_&lt;cpu&gt;.h</b> header files.</p>
<p><b>Example:</b> </p>
<p>The following code section shows the usage of the <b>core_&lt;cpu&gt;.h</b> header files to build a generic library for Cortex-M0, Cortex-M3, Cortex-M4, or Cortex-M7. To select the processor, the source code uses the define <b>CORTEX_M7</b>, <b>CORTEX_M4</b>, <b>CORTEX_M3</b>, <b>CORTEX_M0</b>, or <b>CORTEX_M0PLUS</b>. By using this header file, the source code can access the functions for <a class="el" href="group___core___register__gr.html">Core Register Access</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html">Intrinsic Functions for CPU Instructions</a>, <a class="el" href="group__intrinsic___s_i_m_d__gr.html">Intrinsic Functions for SIMD Instructions [only Cortex-M4 and Cortex-M7]</a>, and <a class="el" href="group___i_t_m___debug__gr.html">Debug Access</a>.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CMSIS_GENERIC              </span><span class="comment">/* disable NVIC and Systick functions */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor">#if defined (CORTEX_M7)</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">  #include &quot;core_cm7.h&quot;</span></div>
<div class="line"><span class="preprocessor">#if defined (CORTEX_M4)</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">  #include &quot;core_cm4.h&quot;</span></div>
<div class="line"><span class="preprocessor">#elif defined (CORTEX_M3)</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">  #include &quot;core_cm3.h&quot;</span></div>
<div class="line"><span class="preprocessor">#elif defined (CORTEX_M0)</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">  #include &quot;core_cm0.h&quot;</span></div>
<div class="line"><span class="preprocessor">#elif defined (CORTEX_M0PLUS)</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">  #include &quot;core_cm0plus.h&quot;</span></div>
<div class="line"><span class="preprocessor">#else</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">  #error &quot;Processor not specified or unsupported.&quot;</span></div>
<div class="line"><span class="preprocessor">#endif</span></div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="_using_pg.html">Using CMSIS in Embedded Applications</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
