var searchData=
[
  ['finite_20impulse_20response_20_28fir_29_20filters',['Finite Impulse Response (FIR) Filters',['../group___f_i_r.html',1,'']]],
  ['finite_20impulse_20response_20_28fir_29_20decimator',['Finite Impulse Response (FIR) Decimator',['../group___f_i_r__decimate.html',1,'']]],
  ['finite_20impulse_20response_20_28fir_29_20interpolator',['Finite Impulse Response (FIR) Interpolator',['../group___f_i_r___interpolate.html',1,'']]],
  ['finite_20impulse_20response_20_28fir_29_20lattice_20filters',['Finite Impulse Response (FIR) Lattice Filters',['../group___f_i_r___lattice.html',1,'']]],
  ['finite_20impulse_20response_20_28fir_29_20sparse_20filters',['Finite Impulse Response (FIR) Sparse Filters',['../group___f_i_r___sparse.html',1,'']]],
  ['fir_20lowpass_20filter_20example',['FIR Lowpass Filter Example',['../group___f_i_r_l_p_f.html',1,'']]],
  ['frequency_20bin_20example',['Frequency Bin Example',['../group___frequency_bin.html',1,'']]],
  ['fast_20math_20functions',['Fast Math Functions',['../group__group_fast_math.html',1,'']]],
  ['filtering_20functions',['Filtering Functions',['../group__group_filters.html',1,'']]]
];
