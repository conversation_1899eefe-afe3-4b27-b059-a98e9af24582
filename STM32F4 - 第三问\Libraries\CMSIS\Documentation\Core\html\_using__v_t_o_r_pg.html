<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Using Interrupt Vector Remap</title>
<title>CMSIS-CORE: Using Interrupt Vector Remap</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_using__v_t_o_r_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Using Interrupt Vector Remap </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>Most Cortex-M processors provide VTOR register for remapping interrupt vectors. The following example shows a typical use case where the interrupt vectors are copied to RAM and the SysTick_Handler is replaced.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &quot;ARMCM3.h&quot;</span>                     <span class="comment">// Device header</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* externals from startup_ARMCM3.s */</span></div>
<div class="line"><span class="keyword">extern</span> uint32_t __Vectors[];                             <span class="comment">/* vector table ROM  */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define VECTORTABLE_SIZE        (256)          </span><span class="comment">/* size Cortex-M3 vector table */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define VECTORTABLE_ALIGNMENT   (0x100ul) </span><span class="comment">/* 16 Cortex + 32 ARMCM3 = 48 words */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>                                          <span class="comment">/* next power of 2 = 256            */</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* new vector table in RAM */</span></div>
<div class="line">uint32_t vectorTable_RAM[VECTORTABLE_SIZE] __attribute__(( aligned (VECTORTABLE_ALIGNMENT) ));</div>
<div class="line"></div>
<div class="line"><span class="comment">/*----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  SysTick_Handler</span></div>
<div class="line"><span class="comment"> *----------------------------------------------------------------------------*/</span></div>
<div class="line"><span class="keyword">volatile</span> uint32_t msTicks = 0;                        <span class="comment">/* counts 1ms timeTicks */</span></div>
<div class="line"><span class="keywordtype">void</span> SysTick_Handler(<span class="keywordtype">void</span>) {</div>
<div class="line">  msTicks++;                                             <span class="comment">/* increment counter */</span></div>
<div class="line">}</div>
<div class="line"></div>
<div class="line"><span class="comment">/*----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  SysTick_Handler (RAM)</span></div>
<div class="line"><span class="comment"> *----------------------------------------------------------------------------*/</span></div>
<div class="line"><span class="keyword">volatile</span> uint32_t msTicks_RAM = 0;                    <span class="comment">/* counts 1ms timeTicks */</span></div>
<div class="line"><span class="keywordtype">void</span> SysTick_Handler_RAM(<span class="keywordtype">void</span>) {</div>
<div class="line">  msTicks_RAM++;                                         <span class="comment">/* increment counter */</span></div>
<div class="line">}</div>
<div class="line"></div>
<div class="line"><span class="comment">/*----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  MAIN function</span></div>
<div class="line"><span class="comment"> *----------------------------------------------------------------------------*/</span></div>
<div class="line"><span class="keywordtype">int</span> main (<span class="keywordtype">void</span>) {</div>
<div class="line">  uint32_t i;</div>
<div class="line">  </div>
<div class="line">  <span class="keywordflow">for</span> (i = 0; i &lt; VECTORTABLE_SIZE; i++) {</div>
<div class="line">    vectorTable_RAM[i] = __Vectors[i];            <span class="comment">/* copy vector table to RAM */</span></div>
<div class="line">  }</div>
<div class="line">                                                   <span class="comment">/* replace SysTick Handler */</span></div>
<div class="line">  vectorTable_RAM[<a class="code" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a6dbff8f8543325f3474cbae2446776e7" title="Exception 15: System Tick Interrupt.">SysTick_IRQn</a> + 16] = (uint32_t)SysTick_Handler_RAM;</div>
<div class="line">        </div>
<div class="line">  <span class="comment">/* relocate vector table */</span> </div>
<div class="line">  <a class="code" href="group___core___register__gr.html#gaeb8e5f7564a8ea23678fe3c987b04013" title="Globally disables interrupts and configurable fault handlers.">__disable_irq</a>();</div>
<div class="line">    SCB-&gt;VTOR = (uint32_t)&amp;vectorTable_RAM;</div>
<div class="line">  <a class="code" href="group__intrinsic___c_p_u__gr.html#gacb2a8ca6eae1ba4b31161578b720c199" title="Data Synchronization Barrier.">__DSB</a>();</div>
<div class="line">  <a class="code" href="group___core___register__gr.html#ga0f98dfbd252b89d12564472dbeba9c27" title="Globally enables interrupts and configurable fault handlers.">__enable_irq</a>();</div>
<div class="line"></div>
<div class="line">  <a class="code" href="group__system__init__gr.html#gae0c36a9591fe6e9c45ecb21a794f0f0f" title="Function to update the variable SystemCoreClock.">SystemCoreClockUpdate</a>();                        <span class="comment">/* Get Core Clock Frequency */</span></div>
<div class="line">  <a class="code" href="group___sys_tick__gr.html#gabe47de40e9b0ad465b752297a9d9f427" title="System Tick Timer Configuration.">SysTick_Config</a>(<a class="code" href="group__system__init__gr.html#gaa3cd3e43291e81e795d642b79b6088e6" title="Variable to hold the system core clock value.">SystemCoreClock</a> / 1000ul); <span class="comment">/* Setup SysTick Timer for 1 msec */</span></div>
<div class="line">  </div>
<div class="line">  <span class="keywordflow">while</span>(1);</div>
<div class="line">}</div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="_using_pg.html">Using CMSIS in Embedded Applications</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
