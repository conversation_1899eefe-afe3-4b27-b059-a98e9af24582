<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CoreDebug_Type Struct Reference</title>
<title>CMSIS-CORE: CoreDebug_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_core_debug___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">CoreDebug_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the Core Debug Register (CoreDebug).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a25c14c022c73a725a1736e903431095d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_core_debug___type.html#a25c14c022c73a725a1736e903431095d">DHCSR</a></td></tr>
<tr class="memdesc:a25c14c022c73a725a1736e903431095d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 (R/W) Debug Halting Control and Status Register.  <a href="#a25c14c022c73a725a1736e903431095d"></a><br/></td></tr>
<tr class="separator:a25c14c022c73a725a1736e903431095d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afefa84bce7497652353a1b76d405d983"><td class="memItemLeft" align="right" valign="top">__O uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_core_debug___type.html#afefa84bce7497652353a1b76d405d983">DCRSR</a></td></tr>
<tr class="memdesc:afefa84bce7497652353a1b76d405d983"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x004 ( /W) Debug Core Register Selector Register.  <a href="#afefa84bce7497652353a1b76d405d983"></a><br/></td></tr>
<tr class="separator:afefa84bce7497652353a1b76d405d983"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab8f4bb076402b61f7be6308075a789c9"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_core_debug___type.html#ab8f4bb076402b61f7be6308075a789c9">DCRDR</a></td></tr>
<tr class="memdesc:ab8f4bb076402b61f7be6308075a789c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x008 (R/W) Debug Core Register Data Register.  <a href="#ab8f4bb076402b61f7be6308075a789c9"></a><br/></td></tr>
<tr class="separator:ab8f4bb076402b61f7be6308075a789c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5cdd51dbe3ebb7041880714430edd52d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_core_debug___type.html#a5cdd51dbe3ebb7041880714430edd52d">DEMCR</a></td></tr>
<tr class="memdesc:a5cdd51dbe3ebb7041880714430edd52d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x00C (R/W) Debug Exception and Monitor Control Register.  <a href="#a5cdd51dbe3ebb7041880714430edd52d"></a><br/></td></tr>
<tr class="separator:a5cdd51dbe3ebb7041880714430edd52d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="ab8f4bb076402b61f7be6308075a789c9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t CoreDebug_Type::DCRDR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afefa84bce7497652353a1b76d405d983"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__O uint32_t CoreDebug_Type::DCRSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5cdd51dbe3ebb7041880714430edd52d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t CoreDebug_Type::DEMCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a25c14c022c73a725a1736e903431095d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t CoreDebug_Type::DHCSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_core_debug___type.html">CoreDebug_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
