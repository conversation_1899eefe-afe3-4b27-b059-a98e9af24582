<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>DWT_Type Struct Reference</title>
<title>CMSIS-CORE: DWT_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_d_w_t___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">DWT_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the Data Watchpoint and Trace Register (DWT).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a37964d64a58551b69ce4c8097210d37d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a37964d64a58551b69ce4c8097210d37d">CTRL</a></td></tr>
<tr class="memdesc:a37964d64a58551b69ce4c8097210d37d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 (R/W) Control Register.  <a href="#a37964d64a58551b69ce4c8097210d37d"></a><br/></td></tr>
<tr class="separator:a37964d64a58551b69ce4c8097210d37d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a71680298e85e96e57002f87e7ab78fd4"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a71680298e85e96e57002f87e7ab78fd4">CYCCNT</a></td></tr>
<tr class="memdesc:a71680298e85e96e57002f87e7ab78fd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x004 (R/W) Cycle Count Register.  <a href="#a71680298e85e96e57002f87e7ab78fd4"></a><br/></td></tr>
<tr class="separator:a71680298e85e96e57002f87e7ab78fd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88cca2ab8eb1b5b507817656ceed89fc"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a88cca2ab8eb1b5b507817656ceed89fc">CPICNT</a></td></tr>
<tr class="memdesc:a88cca2ab8eb1b5b507817656ceed89fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x008 (R/W) CPI Count Register.  <a href="#a88cca2ab8eb1b5b507817656ceed89fc"></a><br/></td></tr>
<tr class="separator:a88cca2ab8eb1b5b507817656ceed89fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0801a2328f3431e4706fed91c828f82"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#ac0801a2328f3431e4706fed91c828f82">EXCCNT</a></td></tr>
<tr class="memdesc:ac0801a2328f3431e4706fed91c828f82"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x00C (R/W) Exception Overhead Count Register.  <a href="#ac0801a2328f3431e4706fed91c828f82"></a><br/></td></tr>
<tr class="separator:ac0801a2328f3431e4706fed91c828f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8afd5a4bf994011748bc012fa442c74d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a8afd5a4bf994011748bc012fa442c74d">SLEEPCNT</a></td></tr>
<tr class="memdesc:a8afd5a4bf994011748bc012fa442c74d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x010 (R/W) Sleep Count Register.  <a href="#a8afd5a4bf994011748bc012fa442c74d"></a><br/></td></tr>
<tr class="separator:a8afd5a4bf994011748bc012fa442c74d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeba92e6c7fd3de4ba06bfd94f47f5b35"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#aeba92e6c7fd3de4ba06bfd94f47f5b35">LSUCNT</a></td></tr>
<tr class="memdesc:aeba92e6c7fd3de4ba06bfd94f47f5b35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x014 (R/W) LSU Count Register.  <a href="#aeba92e6c7fd3de4ba06bfd94f47f5b35"></a><br/></td></tr>
<tr class="separator:aeba92e6c7fd3de4ba06bfd94f47f5b35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35f2315f870a574e3e6958face6584ab"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a35f2315f870a574e3e6958face6584ab">FOLDCNT</a></td></tr>
<tr class="memdesc:a35f2315f870a574e3e6958face6584ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x018 (R/W) Folded-instruction Count Register.  <a href="#a35f2315f870a574e3e6958face6584ab"></a><br/></td></tr>
<tr class="separator:a35f2315f870a574e3e6958face6584ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc5ae11d98da0ad5531a5e979a3c2ab5"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#abc5ae11d98da0ad5531a5e979a3c2ab5">PCSR</a></td></tr>
<tr class="memdesc:abc5ae11d98da0ad5531a5e979a3c2ab5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x01C (R/ ) Program Counter Sample Register.  <a href="#abc5ae11d98da0ad5531a5e979a3c2ab5"></a><br/></td></tr>
<tr class="separator:abc5ae11d98da0ad5531a5e979a3c2ab5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7cf71ff4b30a8362690fddd520763904"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a7cf71ff4b30a8362690fddd520763904">COMP0</a></td></tr>
<tr class="memdesc:a7cf71ff4b30a8362690fddd520763904"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x020 (R/W) Comparator Register 0.  <a href="#a7cf71ff4b30a8362690fddd520763904"></a><br/></td></tr>
<tr class="separator:a7cf71ff4b30a8362690fddd520763904"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bb1c17fc754180cc197b874d3d8673f"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a5bb1c17fc754180cc197b874d3d8673f">MASK0</a></td></tr>
<tr class="memdesc:a5bb1c17fc754180cc197b874d3d8673f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x024 (R/W) Mask Register 0.  <a href="#a5bb1c17fc754180cc197b874d3d8673f"></a><br/></td></tr>
<tr class="separator:a5bb1c17fc754180cc197b874d3d8673f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5fbd9947d110cc168941f6acadc4a729"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a5fbd9947d110cc168941f6acadc4a729">FUNCTION0</a></td></tr>
<tr class="memdesc:a5fbd9947d110cc168941f6acadc4a729"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x028 (R/W) Function Register 0.  <a href="#a5fbd9947d110cc168941f6acadc4a729"></a><br/></td></tr>
<tr class="separator:a5fbd9947d110cc168941f6acadc4a729"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addd893d655ed90d40705b20170daac59"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#addd893d655ed90d40705b20170daac59">RESERVED0</a> [1]</td></tr>
<tr class="memdesc:addd893d655ed90d40705b20170daac59"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#addd893d655ed90d40705b20170daac59"></a><br/></td></tr>
<tr class="separator:addd893d655ed90d40705b20170daac59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a5bb70a5ce3752bd628d5ce5658cb0c"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a4a5bb70a5ce3752bd628d5ce5658cb0c">COMP1</a></td></tr>
<tr class="memdesc:a4a5bb70a5ce3752bd628d5ce5658cb0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x030 (R/W) Comparator Register 1.  <a href="#a4a5bb70a5ce3752bd628d5ce5658cb0c"></a><br/></td></tr>
<tr class="separator:a4a5bb70a5ce3752bd628d5ce5658cb0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c684438a24f8c927e6e01c0e0a605ef"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a0c684438a24f8c927e6e01c0e0a605ef">MASK1</a></td></tr>
<tr class="memdesc:a0c684438a24f8c927e6e01c0e0a605ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x034 (R/W) Mask Register 1.  <a href="#a0c684438a24f8c927e6e01c0e0a605ef"></a><br/></td></tr>
<tr class="separator:a0c684438a24f8c927e6e01c0e0a605ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3345a33476ee58e165447a3212e6d747"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a3345a33476ee58e165447a3212e6d747">FUNCTION1</a></td></tr>
<tr class="memdesc:a3345a33476ee58e165447a3212e6d747"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x038 (R/W) Function Register 1.  <a href="#a3345a33476ee58e165447a3212e6d747"></a><br/></td></tr>
<tr class="separator:a3345a33476ee58e165447a3212e6d747"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a069871233a8c1df03521e6d7094f1de4"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a069871233a8c1df03521e6d7094f1de4">RESERVED1</a> [1]</td></tr>
<tr class="memdesc:a069871233a8c1df03521e6d7094f1de4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a069871233a8c1df03521e6d7094f1de4"></a><br/></td></tr>
<tr class="separator:a069871233a8c1df03521e6d7094f1de4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8927aedbe9fd6bdae8983088efc83332"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a8927aedbe9fd6bdae8983088efc83332">COMP2</a></td></tr>
<tr class="memdesc:a8927aedbe9fd6bdae8983088efc83332"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x040 (R/W) Comparator Register 2.  <a href="#a8927aedbe9fd6bdae8983088efc83332"></a><br/></td></tr>
<tr class="separator:a8927aedbe9fd6bdae8983088efc83332"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ecdc8f0d917dac86b0373532a1c0e2e"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a8ecdc8f0d917dac86b0373532a1c0e2e">MASK2</a></td></tr>
<tr class="memdesc:a8ecdc8f0d917dac86b0373532a1c0e2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x044 (R/W) Mask Register 2.  <a href="#a8ecdc8f0d917dac86b0373532a1c0e2e"></a><br/></td></tr>
<tr class="separator:a8ecdc8f0d917dac86b0373532a1c0e2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acba1654190641a3617fcc558b5e3f87b"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#acba1654190641a3617fcc558b5e3f87b">FUNCTION2</a></td></tr>
<tr class="memdesc:acba1654190641a3617fcc558b5e3f87b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x048 (R/W) Function Register 2.  <a href="#acba1654190641a3617fcc558b5e3f87b"></a><br/></td></tr>
<tr class="separator:acba1654190641a3617fcc558b5e3f87b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8556ca1c32590517602d92fe0cd55738"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a8556ca1c32590517602d92fe0cd55738">RESERVED2</a> [1]</td></tr>
<tr class="memdesc:a8556ca1c32590517602d92fe0cd55738"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a8556ca1c32590517602d92fe0cd55738"></a><br/></td></tr>
<tr class="separator:a8556ca1c32590517602d92fe0cd55738"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3df15697eec279dbbb4b4e9d9ae8b62f"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a3df15697eec279dbbb4b4e9d9ae8b62f">COMP3</a></td></tr>
<tr class="memdesc:a3df15697eec279dbbb4b4e9d9ae8b62f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x050 (R/W) Comparator Register 3.  <a href="#a3df15697eec279dbbb4b4e9d9ae8b62f"></a><br/></td></tr>
<tr class="separator:a3df15697eec279dbbb4b4e9d9ae8b62f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3f01137a8d28c905ddefe7333547fba"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#ae3f01137a8d28c905ddefe7333547fba">MASK3</a></td></tr>
<tr class="memdesc:ae3f01137a8d28c905ddefe7333547fba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x054 (R/W) Mask Register 3.  <a href="#ae3f01137a8d28c905ddefe7333547fba"></a><br/></td></tr>
<tr class="separator:ae3f01137a8d28c905ddefe7333547fba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80bd242fc05ca80f9db681ce4d82e890"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_w_t___type.html#a80bd242fc05ca80f9db681ce4d82e890">FUNCTION3</a></td></tr>
<tr class="memdesc:a80bd242fc05ca80f9db681ce4d82e890"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x058 (R/W) Function Register 3.  <a href="#a80bd242fc05ca80f9db681ce4d82e890"></a><br/></td></tr>
<tr class="separator:a80bd242fc05ca80f9db681ce4d82e890"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="a7cf71ff4b30a8362690fddd520763904"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::COMP0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4a5bb70a5ce3752bd628d5ce5658cb0c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::COMP1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8927aedbe9fd6bdae8983088efc83332"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::COMP2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3df15697eec279dbbb4b4e9d9ae8b62f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::COMP3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a88cca2ab8eb1b5b507817656ceed89fc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::CPICNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a37964d64a58551b69ce4c8097210d37d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::CTRL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a71680298e85e96e57002f87e7ab78fd4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::CYCCNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac0801a2328f3431e4706fed91c828f82"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::EXCCNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a35f2315f870a574e3e6958face6584ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::FOLDCNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5fbd9947d110cc168941f6acadc4a729"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::FUNCTION0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3345a33476ee58e165447a3212e6d747"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::FUNCTION1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acba1654190641a3617fcc558b5e3f87b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::FUNCTION2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a80bd242fc05ca80f9db681ce4d82e890"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::FUNCTION3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aeba92e6c7fd3de4ba06bfd94f47f5b35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::LSUCNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5bb1c17fc754180cc197b874d3d8673f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::MASK0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0c684438a24f8c927e6e01c0e0a605ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::MASK1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8ecdc8f0d917dac86b0373532a1c0e2e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::MASK2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae3f01137a8d28c905ddefe7333547fba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::MASK3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abc5ae11d98da0ad5531a5e979a3c2ab5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t DWT_Type::PCSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="addd893d655ed90d40705b20170daac59"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t DWT_Type::RESERVED0[1]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a069871233a8c1df03521e6d7094f1de4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t DWT_Type::RESERVED1[1]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8556ca1c32590517602d92fe0cd55738"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t DWT_Type::RESERVED2[1]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8afd5a4bf994011748bc012fa442c74d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t DWT_Type::SLEEPCNT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_d_w_t___type.html">DWT_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
