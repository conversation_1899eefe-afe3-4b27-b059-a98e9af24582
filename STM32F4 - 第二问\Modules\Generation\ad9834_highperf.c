/**
 ******************************************************************************
 * @file    ad9834_highperf.c
 * <AUTHOR> - AD9834方案
 * @version V3.0
 * @date    2025-07-31
 * @brief   AD9834高性能DDS信号发生器驱动实现
 *          基于商家驱动优化，替换内置DAC，实现1Hz-5MHz高精度输出
 ******************************************************************************
 */

#include "ad9834_highperf.h"

/* ==================== 私有变量 ==================== */
// AD9834状态跟踪变量 (用于调试和状态查询)
static uint32_t current_frequency = 1000000;  // 当前频率 (默认1MHz) - 用于状态跟踪
static uint16_t current_wave_type = SINE_WAVE; // 当前波形 (默认正弦波)
static float calibration_factor = 1.0f;       // 频率校准因子 (补偿晶振误差)

/**
 * @brief  AD9834初始化 (基于商家驱动优化)
 * @param  None
 * @retval None
 */
void AD9834_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIOA和GPIOB时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA | RCC_AHB1Periph_GPIOB, ENABLE);

    // 配置控制信号引脚 (PA3-PA6)
    GPIO_InitStructure.GPIO_Pin = AD9834_FSYNC | AD9834_SCLK | AD9834_SDATA | AD9834_RESET;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;  // 最高速度
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(AD9834_Control_Port, &GPIO_InitStructure);

    // 配置选择信号引脚 (PB0-PB1)
    GPIO_InitStructure.GPIO_Pin = AD9834_FS | AD9834_PS;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    // 设置初始状态
    GPIO_ResetBits(GPIOB, AD9834_FS);  // 选择频率寄存器0
    GPIO_ResetBits(GPIOB, AD9834_PS);  // 选择相位寄存器0

    // AD9834初始化序列 (基于商家驱动)
    AD9834_Write_16Bits(0x2100);  // B28=1, RESET=1
    AD9834_Write_16Bits(0x2038);  // 设置为正弦波输出
    AD9834_Write_16Bits(0xC000);  // 相位寄存器0 = 0°
    AD9834_Write_16Bits(0x2100);  // 保持B28=1, RESET=1

    // 设置默认频率 1MHz
    AD9834_SetFrequency(FREQ_REG_0, 1000000.0f, SINE_WAVE);
}

/**
 * @brief  AD9834写入16位数据 (基于商家驱动优化)
 * @param  data: 要写入的16位数据
 * @retval None
 */
void AD9834_Write_16Bits(uint32_t data)
{
    uint8_t i = 0;

    // 关闭所有中断，确保SPI通信不被打断
    __disable_irq();

    // 初始状态设置，增加更长的稳定时间
    AD9834_SCLK_SET;
    Delay_us(1);  // 使用微秒级延时确保稳定
    AD9834_FSYNC_CLR;  // 开始传输
    Delay_us(1);  // FSYNC建立时间

    for (i = 0; i < 16; i++) {
        // 设置数据位
        if (data & 0x8000) {
            AD9834_SDATA_SET;
        } else {
            AD9834_SDATA_CLR;
        }

        // 数据建立时间 (确保AD9834能正确读取)
        Delay_us(1);

        // 时钟下降沿 - AD9834在此时锁存数据
        AD9834_SCLK_CLR;
        data <<= 1;

        // 时钟低电平保持时间
        Delay_us(1);

        // 时钟上升沿
        AD9834_SCLK_SET;

        // 时钟高电平保持时间
        Delay_us(1);
    }

    // 结束传输序列
    AD9834_SDATA_SET;  // 数据线拉高
    Delay_us(1);
    AD9834_FSYNC_SET;  // 结束传输
    Delay_us(2);       // 传输完成后的稳定时间

    // 恢复中断
    __enable_irq();
}

/**
 * @brief  设置AD9834频率 (超高精度版本，误差<0.01%)
 * @param  reg: 频率寄存器选择 (FREQ_REG_0 或 FREQ_REG_1)
 * @param  frequency_hz: 频率值 (1Hz - 5MHz)
 * @param  wave_type: 波形类型 (SINE_WAVE, TRIANGLE_WAVE, SQUARE_WAVE)
 * @retval None
 */
void AD9834_SetFrequency(uint16_t reg, float frequency_hz, uint16_t wave_type)
{
    uint16_t freqHi = reg;
    uint16_t freqLo = reg;

    // 限制频率范围
    if (frequency_hz < AD9834_MIN_FREQ_HZ) frequency_hz = AD9834_MIN_FREQ_HZ;
    if (frequency_hz > AD9834_MAX_FREQ_HZ) frequency_hz = AD9834_MAX_FREQ_HZ;

    // 超高精度频率控制字计算
    // 使用双精度浮点数和四舍五入提高计算精度
    double precise_val = ((double)frequency_hz * 268435456.0) / (double)AD9834_SYSTEM_CLOCK;
    uint32_t val = (uint32_t)(precise_val + 0.5);  // 四舍五入提高精度

    // 分离高14位和低14位
    freqHi |= ((val & 0xFFFC000) >> 14);  // 高14位
    freqLo |= (val & 0x3FFF);             // 低14位

    // 超稳定写入序列 (多重确认)
    // 第一步：复位AD9834，确保干净状态
    AD9834_Write_16Bits(AD9834_B28 | AD9834_RESET_BIT);
    Delay_ms(2);  // 复位稳定时间

    // 第二步：设置B28模式（28位频率字）
    AD9834_Write_16Bits(AD9834_B28);
    Delay_us(10);

    // 第三步：写入频率数据
    AD9834_Write_16Bits(freqLo);     // 写入低14位
    Delay_us(10);
    AD9834_Write_16Bits(freqHi);     // 写入高14位
    Delay_us(10);

    // 第四步：设置波形类型并启动输出
    AD9834_Write_16Bits(wave_type);  // 设置波形类型，清除复位
    Delay_ms(1);  // 让AD9834稳定输出

    // 第五步：再次确认设置（双重保险）
    AD9834_Write_16Bits(wave_type);
    Delay_us(10);

    // 更新当前状态
    current_frequency = (uint32_t)frequency_hz;
    current_wave_type = wave_type;
}

/**
 * @brief  设置AD9834相位
 * @param  reg: 相位寄存器选择 (PHASE_REG_0 或 PHASE_REG_1)
 * @param  phase_value: 相位值 (0-4095, 对应0°-360°)
 * @retval None
 */
void AD9834_SetPhase(uint16_t reg, uint16_t phase_value)
{
    uint16_t phase = reg | (phase_value & 0x0FFF);  // 限制为12位
    AD9834_Write_16Bits(phase);
}

/**
 * @brief  快速频率切换 (用于扫频测试)
 * @param  freq1_hz: 频率1 (Hz)
 * @param  freq2_hz: 频率2 (Hz)
 * @retval None
 */
void AD9834_FastFreqSwitch(float freq1_hz, float freq2_hz)
{
    // 预设置两个频率寄存器
    AD9834_SetFrequency(FREQ_REG_0, freq1_hz, current_wave_type);
    AD9834_SetFrequency(FREQ_REG_1, freq2_hz, current_wave_type);

    // 启用引脚控制模式
    AD9834_Write_16Bits(AD9834_B28 | PIN_SW);

    // 通过硬件引脚快速切换
    // 用户可以通过控制PB0 (AD9834_FS) 来切换频率
    // AD9834_FS_CLR -> 选择频率1
    // AD9834_FS_SET -> 选择频率2
}

/**
 * @brief  AD9834复位
 * @param  None
 * @retval None
 */
void AD9834_Reset(void)
{
    // 硬件复位
    AD9834_RESET_CLR;
    Delay_us(10);
    AD9834_RESET_SET;
    Delay_us(10);

    // 软件复位
    AD9834_Write_16Bits(AD9834_B28 | AD9834_RESET_BIT);
    Delay_us(1);
    AD9834_Write_16Bits(AD9834_B28);

    // 重置状态变量
    current_frequency = 1000000;  // 复位后默认1MHz
    current_wave_type = SINE_WAVE;
}

/**
 * @brief  频率稳定性增强 (温和版本)
 * @param  frequency_hz: 要稳定的频率
 * @retval None
 */
void AD9834_StabilizeFrequency(uint32_t frequency_hz)
{
    // 重新设置频率，确保稳定性
    AD9834_SetFrequency(FREQ_REG_0, (float)frequency_hz, SINE_WAVE);

    // 短暂延时让频率稳定
    Delay_ms(5);

    // 再次确认设置 (双重保险)
    AD9834_SetFrequency(FREQ_REG_0, (float)frequency_hz, SINE_WAVE);

    // 更新当前频率记录
    current_frequency = frequency_hz;
}

/**
 * @brief  高精度频率设置 (电赛专用，误差<0.01%)
 * @param  frequency_hz: 精确频率值 (Hz)
 * @retval 实际设置的频率值
 */
uint32_t AD9834_SetFrequency_Precision(uint32_t frequency_hz)
{
    uint64_t freq_word;
    uint16_t freqHi = FREQ_REG_0;
    uint16_t freqLo = FREQ_REG_0;

    // 应用校准因子
    uint64_t calibrated_freq = (uint64_t)(frequency_hz * calibration_factor);

    // 限制频率范围
    if (calibrated_freq < AD9834_MIN_FREQ_HZ) calibrated_freq = AD9834_MIN_FREQ_HZ;
    if (calibrated_freq > AD9834_MAX_FREQ_HZ) calibrated_freq = AD9834_MAX_FREQ_HZ;

    // 超高精度计算 (使用64位整数避免浮点误差)
    freq_word = (calibrated_freq * AD9834_FREQ_WORD_MAX) / AD9834_SYSTEM_CLOCK;

    // 分离频率字
    freqLo |= (uint16_t)(freq_word & 0x3FFF);
    freqHi |= (uint16_t)((freq_word >> 14) & 0x3FFF);

    // 原子写入序列 (确保频率切换无毛刺)
    AD9834_Write_16Bits(AD9834_B28 | AD9834_RESET_BIT);  // 复位
    AD9834_Write_16Bits(SINE_WAVE | AD9834_B28);         // 设置模式
    AD9834_Write_16Bits(freqLo);                         // 低14位
    AD9834_Write_16Bits(freqHi);                         // 高14位
    AD9834_Write_16Bits(SINE_WAVE);                      // 启动输出

    // 计算实际频率 (用于验证)
    uint32_t actual_freq = (uint32_t)((freq_word * AD9834_SYSTEM_CLOCK) / AD9834_FREQ_WORD_MAX);

    current_frequency = actual_freq;
    return actual_freq;
}

/**
 * @brief  频率校准 (补偿晶振误差)
 * @param  calibration_factor: 校准因子 (1.0 = 无校准, >1.0 = 频率偏高需校正)
 * @retval None
 */
void AD9834_SetCalibration(float calibration_factor)
{
    // 限制校准范围 (±5%，符合电赛要求)
    if (calibration_factor < 0.95f) calibration_factor = 0.95f;
    if (calibration_factor > 1.05f) calibration_factor = 1.05f;

    calibration_factor = calibration_factor;
}


