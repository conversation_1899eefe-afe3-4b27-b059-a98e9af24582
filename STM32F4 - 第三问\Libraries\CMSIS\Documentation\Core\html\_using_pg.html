<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Using CMSIS in Embedded Applications</title>
<title>CMSIS-CORE: Using CMSIS in Embedded Applications</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_using_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Using CMSIS in Embedded Applications </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>To use the CMSIS-CORE the following files are added to the embedded application:</p>
<ul>
<li><a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a> with reset handler and exception vectors.</li>
<li><a class="el" href="system_c_pg.html">System Configuration Files system_&lt;device&gt;.c and system_&lt;device&gt;.h</a> with general device configuration (i.e. for clock and BUS setup).</li>
<li><a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> gives access to processor core and all peripherals.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>The files <a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a> and <a class="el" href="system_c_pg.html">System Configuration Files system_&lt;device&gt;.c and system_&lt;device&gt;.h</a> may require application specific adaptations and therefore should be copied into the application project folder prior configuration. The <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is included in all source files that need device access and can be stored on a central include folder that is generic for all projects.</dd></dl>
<p>The <a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a> is executed after reset and calls <a class="el" href="group__system__init__gr.html#ga93f514700ccf00d08dbdcff7f1224eb2">SystemInit</a>. After the system initialization control is transferred to the C/C++ run-time library which performs initialization and calls the <b>main</b> function in the user code. In addition the <a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a> contains all exception and interrupt vectors and implements a default function for every interrupt. It may also contain stack and heap configurations for the user application.</p>
<p>The <a class="el" href="system_c_pg.html">System Configuration Files system_&lt;device&gt;.c and system_&lt;device&gt;.h</a> performs the setup for the processor clock. The variable <a class="el" href="group__system__init__gr.html#gaa3cd3e43291e81e795d642b79b6088e6">SystemCoreClock</a> indicates the CPU clock speed. <a class="el" href="group__system__init__gr.html">System and Clock Configuration</a> describes the minimum feature set. In addition the file may contain functions for the memory BUS setup and clock re-configuration.</p>
<p>The <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> is the central include file that the application programmer is using in the C source code. It provides the following features:</p>
<ul>
<li><a class="el" href="group__peripheral__gr.html">Peripheral Access</a> provides a standardized register layout for all peripherals. Optionally functions for device-specific peripherals may be available.</li>
<li><a class="el" href="group___n_v_i_c__gr.html">Interrupts and Exceptions (NVIC)</a> can be accessed with standardized symbols and functions for the Nested Interrupt Vector Controller (NVIC) are provided.</li>
<li><a class="el" href="group__intrinsic___c_p_u__gr.html">Intrinsic Functions for CPU Instructions</a> allow to access special instructions, for example for activating sleep mode or the NOP instruction.</li>
<li><a class="el" href="group__intrinsic___s_i_m_d__gr.html">Intrinsic Functions for SIMD Instructions [only Cortex-M4 and Cortex-M7]</a> provide access to the DSP-oriented instructions.</li>
<li><a class="el" href="group___sys_tick__gr.html">Systick Timer (SYSTICK)</a> function to configure and start a periodic timer interrupt.</li>
<li><a class="el" href="group___i_t_m___debug__gr.html">Debug Access</a> are functions that allow printf-style I/O via the CoreSight Debug Unit and ITM communication.</li>
</ul>
<div class="image">
<img src="CMSIS_CORE_Files_user.png" alt="CMSIS_CORE_Files_user.png"/>
<div class="caption">
CMSIS-CORE User Files</div></div>
<p> The CMSIS-CORE are device specific. In addition, the <a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a> is also compiler vendor specific. The various compiler vendor tool chains may provide folders that contain the CMSIS files for each supported device.</p>
<p>For example, the following files are provided in MDK-ARM to support the STM32F10x Connectivity Line device variants:</p>
<table  class="cmtable">
<tr>
<th>File </th><th>Description  </th></tr>
<tr>
<td>".\ARM\Startup\ST\STM32F10x\startup_stm32f10x_cl.s" </td><td><a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a> for the STM32F10x Connectivity Line device variants.  </td></tr>
<tr>
<td>".\ARM\Startup\ST\STM32F10x\system_stmf10x.c" </td><td><a class="el" href="system_c_pg.html">System Configuration Files system_&lt;device&gt;.c and system_&lt;device&gt;.h</a> for the STM32F10x device families.  </td></tr>
<tr>
<td>".\ARM\INC\ST\STM32F10x\stm32f10x.h" </td><td><a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> for the STM32F10x device families.  </td></tr>
<tr>
<td>".\ARM\INC\ST\STM32F10x\system_stm32f10x.h" </td><td><a class="el" href="system_c_pg.html#system_Device_h_sec">system_Device.h Template File</a> for the STM32F10x device families.  </td></tr>
</table>
<dl class="section note"><dt>Note</dt><dd>The silicon vendors create these device-specific CMSIS-CORE files based on <a class="el" href="_templates_pg.html">Template Files</a> provide by ARM.</dd></dl>
<p>Thereafter, the functions described under <a href="Modules.html"><b>Reference</b> </a> can be used in the application.</p>
<p><b>Examples</b> </p>
<ul>
<li><a class="el" href="_using__c_m_s_i_s.html">Basic CMSIS Example</a> is a simple example that shows the usage of the CMSIS layer.</li>
<li><a class="el" href="_using__v_t_o_r_pg.html">Using Interrupt Vector Remap</a> shows how to remap the interrupt vector table.</li>
<li><a class="el" href="_using__a_r_m_pg.html">Using CMSIS with generic ARM Processors</a> explains how to use CMSIS-CORE for ARM processors. </li>
</ul>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
