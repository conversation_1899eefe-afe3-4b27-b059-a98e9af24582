<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Template Files</title>
<title>CMSIS-CORE: Template Files</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_templates_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Template Files </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>ARM supplies CMSIS-CORE template files for the all supported Cortex-M processors and various compiler vendors. Refer to the list of <a class="el" href="index.html#tested_tools_sec">Tested and Verified Toolchains</a> for compliancy. These template files include the following:</p>
<ul>
<li>Register names of the Core Peripherals and names of the Core Exception Vectors.</li>
<li>Functions to access core peripherals, special CPU instructions and SIMD instructions (for Cortex-M4 and Cortex-M7)</li>
<li>Generic startup code and system configuration code.</li>
</ul>
<p>The detailed file structure of the CMSIS-CORE is shown in the following picture.</p>
<div class="image">
<img src="CMSIS_CORE_Files.png" alt="CMSIS_CORE_Files.png"/>
<div class="caption">
CMSIS-CORE File Structure</div></div>
 <h1><a class="anchor" id="template_files_sec"></a>
Template Files</h1>
<p>The CMSIS-CORE template files should be extended by the silicon vendor to reflect the actual device and device peripherals. Silicon vendors add in this context the:</p>
<ul>
<li><b>Device Peripheral Access Layer</b> that provides definitions for device-specific peripherals.</li>
<li><b>Access Functions for Peripherals</b> (optional) that provides additional helper functions to access device-specific peripherals.</li>
<li><b>Interrupt vectors</b> in the startup file that are device specific.</li>
</ul>
<table  class="cmtable">
<tr>
<th>Template File </th><th>Description  </th></tr>
<tr>
<td>.\Device\_Template_Vendor\Vendor\Device\Source\ARM\startup_Device.s </td><td>Startup file template for ARM C/C++ Compiler.  </td></tr>
<tr>
<td>.\Device\_Template_Vendor\Vendor\Device\Source\GCC\startup_Device.s </td><td>Startup file template for GNU GCC ARM Embedded Compiler.  </td></tr>
<tr>
<td>.\Device\_Template_Vendor\Vendor\Device\Source\IAR\startup_Device.s </td><td>Startup file template for IAR C/C++ Compiler.  </td></tr>
<tr>
<td>.\Device\_Template_Vendor\Vendor\Device\Source\system_Device.c </td><td>Generic system_Device.c file for system configuration (i.e. processor clock and memory bus system).  </td></tr>
<tr>
<td>.\Device\_Template_Vendor\Vendor\Device\Include\Device.h </td><td>Generic device header file. Needs to be extended with the device-specific peripheral registers. Optionally functions that access the peripherals can be part of that file.  </td></tr>
<tr>
<td>.\Device\_Template_Vendor\Vendor\Device\Include\system_Device.h </td><td>Generic system device configuration include file.  </td></tr>
</table>
<p>In addition ARM provides the following core header files that do not need any modifications.</p>
<table  class="cmtable">
<tr>
<th>Core Header Files </th><th>Description  </th></tr>
<tr>
<td><b>core_&lt;cpu&gt;.h</b> </td><td>Defines the core peripherals and provides helper functions that access the core registers. This file is available for all supported processors:<ul>
<li>core_cm0.h: for the Cortex-M0 processor</li>
<li>core_cm0plus.h: for the Cortex-M0+ processor</li>
<li>core_cm3.h: for the Cortex-M3 processor</li>
<li>core_cm4.h: for the Cortex-M4 processor</li>
<li>core_cm7.h: for the Cortex-M7 processor</li>
<li>core_sc000.h: for the SecurCore SC000 processor</li>
<li>core_sc300.h: for the SecurCore SC300 processor   </li>
</ul>
</td></tr>
<tr>
<td><b>core_cmInstr.h</b> </td><td>Defines intrinsic functions to access special Cortex-M instructions.  </td></tr>
<tr>
<td><b>core_cmFunc.h</b> </td><td>Defines functions to access the Cortex-M core peripherals.  </td></tr>
<tr>
<td><b>core_cm4_simd.h</b> </td><td>Defines intrinsic functions to access the SIMD instructions for Cortex-M4 and Cortex-M7.  </td></tr>
</table>
<h1><a class="anchor" id="adapt_template_files_sec"></a>
Adaption of Template Files to Devices</h1>
<p>Copy the complete folder including files and replace:</p>
<ul>
<li>folder name 'Vendor' with the abbreviation for the device vendor e.g.: NXP.</li>
<li>folder name 'Device' with the specific device name e.g.: LPC17xx.</li>
<li>in the filenames 'Device' with the specific device name e.g.: LPC17xx.</li>
</ul>
<p>Each template file contains comments that start with <b>ToDo:</b> that describe a required modification. The template files contain placeholders:</p>
<table  class="cmtable">
<tr>
<th>Placeholder </th><th>Replaced with  </th></tr>
<tr>
<td>&lt;Device&gt; </td><td>the specific device name or device family name; i.e. LPC17xx.  </td></tr>
<tr>
<td>&lt;DeviceInterrupt&gt; </td><td>a specific interrupt name of the device; i.e. TIM1 for Timer 1. </td></tr>
<tr>
<td>&lt;DeviceAbbreviation&gt; </td><td>short name or abbreviation of the device family; i.e. LPC.  </td></tr>
<tr>
<td>Cortex-M# </td><td>the specific Cortex-M processor name; i.e. Cortex-M3.  </td></tr>
</table>
<p>The adaption of the template files is described in detail on the following pages:</p>
<ul>
<li><a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a></li>
<li><a class="el" href="system_c_pg.html">System Configuration Files system_&lt;device&gt;.c and system_&lt;device&gt;.h</a></li>
<li><a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> </li>
</ul>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
